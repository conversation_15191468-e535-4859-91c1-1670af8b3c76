{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Resume-Matcher-main/apps/frontend/components/jd-upload/text-area.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/jd-upload/text-area.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/jd-upload/text-area.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsS,GACnU,oEACA", "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Resume-Matcher-main/apps/frontend/components/jd-upload/text-area.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/jd-upload/text-area.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/jd-upload/text-area.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkR,GAC/S,gDACA", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Resume-Matcher-main/apps/frontend/components/common/dot-pattern-glow.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const DotPattern = registerClientReference(\n    function() { throw new Error(\"Attempted to call DotPattern() from the server but DotPattern is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/dot-pattern-glow.tsx <module evaluation>\",\n    \"DotPattern\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,wEACA", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Resume-Matcher-main/apps/frontend/components/common/dot-pattern-glow.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const DotPattern = registerClientReference(\n    function() { throw new Error(\"Attempted to call DotPattern() from the server but DotPattern is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/common/dot-pattern-glow.tsx\",\n    \"DotPattern\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,oDACA", "debugId": null}}, {"offset": {"line": 96, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Resume-Matcher-main/apps/frontend/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\n/**\n * Combines multiple class names or class name objects into a single string.\n * Uses clsx for conditional class logic and twMerge for Tailwind CSS class deduplication.\n * \n * @param inputs - Class values to be combined (strings, objects, arrays)\n * @returns A string of combined and optimized class names\n */\nexport function cn(...inputs: ClassValue[]): string {\n  return twMerge(clsx(inputs));\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AASO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Resume-Matcher-main/apps/frontend/components/common/background-container.tsx"], "sourcesContent": ["import { DotPattern } from '@/components/common/dot-pattern-glow';\nimport { cn } from '@/lib/utils';\n\n/**\n * BackgroundContainer Component\n *\n * Provides a full-screen section with a gradient background,\n * a dark inner container with rounded corners, and a glowing dot pattern.\n * It accepts children elements to render inside the inner container.\n *\n * @param {object} props - The component props.\n * @param {React.ReactNode} props.children - The content to be rendered inside the container.\n * @param {string} [props.className] - Optional additional class names for the section element.\n * @param {string} [props.innerClassName] - Optional additional class names for the inner div element.\n * @param {string} [props.dotClassName] - Optional additional class names for the DotPattern component.\n * @returns {JSX.Element} The rendered BackgroundContainer component.\n */\n\ninterface BackgroundContainerProps {\n\tchildren: React.ReactNode;\n\tclassName?: string;\n\tinnerClassName?: string;\n\tdotClassName?: string;\n}\n\nconst BackgroundContainer = ({\n\tchildren,\n\tclassName,\n\tinnerClassName,\n\tdotClassName,\n}: BackgroundContainerProps) => {\n\treturn (\n\t\t<section\n\t\t\tclassName={cn(\n\t\t\t\t'relative flex h-screen items-center justify-center overflow-hidden p-2 bg-gradient-to-br from-pink-600 via-orange-400 to-purple-700',\n\t\t\t\tclassName,\n\t\t\t)}\n\t\t>\n\t\t\t{/* Inner container with dark background, padding, and rounded corners */}\n\t\t\t<div\n\t\t\t\tclassName={cn(\n\t\t\t\t\t'relative z-10 flex h-full w-full flex-col items-center justify-center bg-zinc-950 p-8 rounded-2xl',\n\t\t\t\t\tinnerClassName, // Allow overriding or extending inner div styles\n\t\t\t\t)}\n\t\t\t>\n\t\t\t\t{/* Dot pattern component for visual effect */}\n\t\t\t\t<DotPattern\n\t\t\t\t\tcr={2} // Circle radius for dots\n\t\t\t\t\tglow={true} // Enable glow effect\n\t\t\t\t\tclassName={cn(\n\t\t\t\t\t\t'absolute inset-0 -z-10 text-violet-400 [mask-image:radial-gradient(400px_circle_at_center,white,transparent)]',\n\t\t\t\t\t\tdotClassName,\n\t\t\t\t\t)}\n\t\t\t\t/>\n\t\t\t\t{/* Render children content above the dot pattern */}\n\t\t\t\t<div className=\"relative z-10 w-full h-full flex flex-col items-center justify-center\">\n\t\t\t\t\t{children}\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</section>\n\t);\n};\n\nexport default BackgroundContainer;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAwBA,MAAM,sBAAsB,CAAC,EAC5B,QAAQ,EACR,SAAS,EACT,cAAc,EACd,YAAY,EACc;IAC1B,qBACC,8OAAC;QACA,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACX,uIACA;kBAID,cAAA,8OAAC;YACA,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACX,qGACA;;8BAID,8OAAC,+IAAA,CAAA,aAAU;oBACV,IAAI;oBACJ,MAAM;oBACN,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACX,iHACA;;;;;;8BAIF,8OAAC;oBAAI,WAAU;8BACb;;;;;;;;;;;;;;;;;AAKN;uCAEe", "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Resume-Matcher-main/apps/frontend/app/%28default%29/%28onboarding%29/jobs/page.tsx"], "sourcesContent": ["import JobDescriptionUploadTextArea from '@/components/jd-upload/text-area';\nimport BackgroundContainer from '@/components/common/background-container';\nimport { Suspense } from 'react';\n\nconst ProvideJobDescriptionsPage = () => {\n\treturn (\n\t\t<BackgroundContainer>\n\t\t\t<div className=\"flex flex-col items-center justify-center max-w-7xl\">\n\t\t\t\t<h1 className=\"text-6xl font-bold text-center mb-12 text-white\">\n\t\t\t\t\tProvide Job Descriptions\n\t\t\t\t</h1>\n\t\t\t\t<p className=\"text-center text-gray-300 text-xl mb-8 max-w-xl mx-auto\">\n\t\t\t\t\tPaste up to three job descriptions below. We&apos;ll use these to compare\n\t\t\t\t\tagainst your resume and find the best matches.\n\t\t\t\t</p>\n\t\t\t\t<Suspense fallback={<div>Loading input...</div>}>\n\t\t\t\t\t<JobDescriptionUploadTextArea />\n\t\t\t\t</Suspense>\n\t\t\t</div>\n\t\t</BackgroundContainer>\n\t);\n};\n\nexport default ProvideJobDescriptionsPage;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,6BAA6B;IAClC,qBACC,8OAAC,gJAAA,CAAA,UAAmB;kBACnB,cAAA,8OAAC;YAAI,WAAU;;8BACd,8OAAC;oBAAG,WAAU;8BAAkD;;;;;;8BAGhE,8OAAC;oBAAE,WAAU;8BAA0D;;;;;;8BAIvE,8OAAC,qMAAA,CAAA,WAAQ;oBAAC,wBAAU,8OAAC;kCAAI;;;;;;8BACxB,cAAA,8OAAC,2IAAA,CAAA,UAA4B;;;;;;;;;;;;;;;;;;;;;AAKlC;uCAEe", "debugId": null}}]}