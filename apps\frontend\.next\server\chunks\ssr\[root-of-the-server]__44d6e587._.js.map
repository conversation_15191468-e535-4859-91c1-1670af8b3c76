{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Resume-Matcher-main/apps/frontend/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\n/**\n * Combines multiple class names or class name objects into a single string.\n * Uses clsx for conditional class logic and twMerge for Tailwind CSS class deduplication.\n * \n * @param inputs - Class values to be combined (strings, objects, arrays)\n * @returns A string of combined and optimized class names\n */\nexport function cn(...inputs: ClassValue[]): string {\n  return twMerge(clsx(inputs));\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AASO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Resume-Matcher-main/apps/frontend/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nfunction Textarea({ className, ...props }: React.ComponentProps<'textarea'>) {\n\treturn (\n\t\t<textarea\n\t\t\tdata-slot=\"textarea\"\n\t\t\tclassName={cn(\n\t\t\t\t'border-input text-white placeholder:text-muted-foreground/70 focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex min-h-19.5 w-full rounded-md border bg-transparent px-3 py-2 text-sm shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50',\n\t\t\t\tclassName,\n\t\t\t)}\n\t\t\t{...props}\n\t\t/>\n\t);\n}\nTextarea.displayName = 'Textarea';\n\nexport { Textarea };\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IAC1E,qBACC,8OAAC;QACA,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACX,oaACA;QAEA,GAAG,KAAK;;;;;;AAGZ;AACA,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Resume-Matcher-main/apps/frontend/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Slot } from '@radix-ui/react-slot';\nimport { cva, type VariantProps } from 'class-variance-authority';\n\nimport { cn } from '@/lib/utils';\n\nconst buttonVariants = cva(\n\t\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n\t{\n\t\tvariants: {\n\t\t\tvariant: {\n\t\t\t\tdefault: 'bg-primary text-primary-foreground shadow-sm hover:bg-primary/90',\n\t\t\t\tdestructive:\n\t\t\t\t\t'bg-destructive text-destructive-foreground shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40',\n\t\t\t\toutline:\n\t\t\t\t\t'border border-input bg-transparent shadow-sm hover:bg-accent hover:text-accent-foreground focus-visible:ring-ring/30',\n\t\t\t\tsecondary: 'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\n\t\t\t\tghost: 'hover:bg-accent hover:text-accent-foreground',\n\t\t\t\tlink: 'text-primary underline-offset-4 hover:underline',\n\t\t\t},\n\t\t\tsize: {\n\t\t\t\tdefault: 'h-9 px-4 py-2',\n\t\t\t\tsm: 'h-8 rounded-md px-3 text-xs',\n\t\t\t\tlg: 'h-10 rounded-md px-8',\n\t\t\t\ticon: 'size-9',\n\t\t\t},\n\t\t},\n\t\tdefaultVariants: {\n\t\t\tvariant: 'default',\n\t\t\tsize: 'default',\n\t\t},\n\t},\n);\n\nfunction Button({\n\tclassName,\n\tvariant,\n\tsize,\n\tasChild = false,\n\t...props\n}: React.ComponentProps<'button'> &\n\tVariantProps<typeof buttonVariants> & {\n\t\tasChild?: boolean;\n\t}) {\n\tconst Comp = asChild ? Slot : 'button';\n\n\treturn (\n\t\t<Comp\n\t\t\tdata-slot=\"button\"\n\t\t\tclassName={cn(buttonVariants({ variant, size, className }))}\n\t\t\t{...props}\n\t\t/>\n\t);\n}\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACxB,8VACA;IACC,UAAU;QACT,SAAS;YACR,SAAS;YACT,aACC;YACD,SACC;YACD,WAAW;YACX,OAAO;YACP,MAAM;QACP;QACA,MAAM;YACL,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACP;IACD;IACA,iBAAiB;QAChB,SAAS;QACT,MAAM;IACP;AACD;AAGD,SAAS,OAAO,EACf,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACC,8OAAC;QACA,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGZ", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Resume-Matcher-main/apps/frontend/lib/api/resume.ts"], "sourcesContent": ["import { ImprovedResult } from '@/components/common/resume_previewer_context';\n\nconst API_URL = process.env.NEXT_PUBLIC_API_URL!;\n\n/** Uploads job descriptions and returns a job_id */\nexport async function uploadJobDescriptions(\n    descriptions: string[],\n    resumeId: string\n): Promise<string> {\n    const res = await fetch(`${API_URL}/api/v1/jobs/upload`, {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ job_descriptions: descriptions, resume_id: resumeId }),\n    });\n    if (!res.ok) throw new Error(`Upload failed with status ${res.status}`);\n    const data = await res.json();\n    console.log('Job upload response:', data);\n    return data.job_id[0];\n}\n\n/** Improves the resume and returns the full preview object */\nexport async function improveResume(\n    resumeId: string,\n    jobId: string\n): Promise<ImprovedResult> {\n    let response: Response;\n    try {\n        response = await fetch(`${API_URL}/api/v1/resumes/improve`, {\n            method: 'POST',\n            headers: { 'Content-Type': 'application/json' },\n            body: JSON.stringify({ resume_id: resumeId, job_id: jobId }),\n        });\n    } catch (networkError) {\n        console.error('Network error during improveResume:', networkError);\n        throw networkError;\n    }\n\n    const text = await response.text();\n    if (!response.ok) {\n        console.error('Improve failed response body:', text);\n        throw new Error(`Improve failed with status ${response.status}: ${text}`);\n    }\n\n    let data: ImprovedResult;\n    try {\n        data = JSON.parse(text) as ImprovedResult;\n    } catch (parseError) {\n        console.error('Failed to parse improveResume response:', parseError, 'Raw response:', text);\n        throw parseError;\n    }\n\n    console.log('Resume improvement response:', data);\n    return data;\n}"], "names": [], "mappings": ";;;;AAEA,MAAM;AAGC,eAAe,sBAClB,YAAsB,EACtB,QAAgB;IAEhB,MAAM,MAAM,MAAM,MAAM,GAAG,QAAQ,mBAAmB,CAAC,EAAE;QACrD,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,MAAM,KAAK,SAAS,CAAC;YAAE,kBAAkB;YAAc,WAAW;QAAS;IAC/E;IACA,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,IAAI,MAAM,EAAE;IACtE,MAAM,OAAO,MAAM,IAAI,IAAI;IAC3B,QAAQ,GAAG,CAAC,wBAAwB;IACpC,OAAO,KAAK,MAAM,CAAC,EAAE;AACzB;AAGO,eAAe,cAClB,QAAgB,EAChB,KAAa;IAEb,IAAI;IACJ,IAAI;QACA,WAAW,MAAM,MAAM,GAAG,QAAQ,uBAAuB,CAAC,EAAE;YACxD,QAAQ;YACR,SAAS;gBAAE,gBAAgB;YAAmB;YAC9C,MAAM,KAAK,SAAS,CAAC;gBAAE,WAAW;gBAAU,QAAQ;YAAM;QAC9D;IACJ,EAAE,OAAO,cAAc;QACnB,QAAQ,KAAK,CAAC,uCAAuC;QACrD,MAAM;IACV;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAChC,IAAI,CAAC,SAAS,EAAE,EAAE;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,MAAM;IAC5E;IAEA,IAAI;IACJ,IAAI;QACA,OAAO,KAAK,KAAK,CAAC;IACtB,EAAE,OAAO,YAAY;QACjB,QAAQ,KAAK,CAAC,2CAA2C,YAAY,iBAAiB;QACtF,MAAM;IACV;IAEA,QAAQ,GAAG,CAAC,gCAAgC;IAC5C,OAAO;AACX", "debugId": null}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Resume-Matcher-main/apps/frontend/components/jd-upload/text-area.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useCallback } from 'react';\nimport { useRouter, useSearchParams } from 'next/navigation';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Button } from '@/components/ui/button';\nimport { useResumePreview } from '@/components/common/resume_previewer_context';\nimport { uploadJobDescriptions, improveResume } from '@/lib/api/resume';\n\ntype SubmissionStatus = 'idle' | 'submitting' | 'success' | 'error';\ntype ImprovementStatus = 'idle' | 'improving' | 'error';\n\nexport default function JobDescriptionUploadTextArea() {\n\tconst [text, setText] = useState('');\n\tconst [flash, setFlash] = useState<{ type: 'error' | 'success'; message: string } | null>(null);\n\tconst [submissionStatus, setSubmissionStatus] = useState<SubmissionStatus>('idle');\n\tconst [improvementStatus, setImprovementStatus] = useState<ImprovementStatus>('idle');\n\tconst [jobId, setJobId] = useState<string | null>(null);\n\n\tconst { setImprovedData } = useResumePreview();\n\tconst resumeId = useSearchParams().get('resume_id')!;\n\tconst router = useRouter();\n\n\tconst handleChange = useCallback(\n\t\t(e: React.ChangeEvent<HTMLTextAreaElement>) => {\n\t\t\tsetText(e.target.value);\n\t\t\tsetFlash(null);\n\t\t\tif (submissionStatus !== 'idle') setSubmissionStatus('idle');\n\t\t},\n\t\t[submissionStatus]\n\t);\n\n\tconst handleUpload = useCallback(\n\t\tasync (e: React.FormEvent) => {\n\t\t\te.preventDefault();\n\t\t\tconst trimmed = text.trim();\n\t\t\tif (!trimmed) {\n\t\t\t\tsetFlash({ type: 'error', message: 'Job description cannot be empty.' });\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (!resumeId) {\n\t\t\t\tsetFlash({ type: 'error', message: 'Missing resume ID.' });\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tsetSubmissionStatus('submitting');\n\t\t\ttry {\n\t\t\t\tconst id = await uploadJobDescriptions([trimmed], resumeId);\n\t\t\t\tsetJobId(id);\n\t\t\t\tsetSubmissionStatus('success');\n\t\t\t\tsetFlash({ type: 'success', message: 'Job description submitted successfully!' });\n\t\t\t} catch (err) {\n\t\t\t\tconsole.error(err);\n\t\t\t\tsetSubmissionStatus('error');\n\t\t\t\tsetFlash({ type: 'error', message: (err as Error).message });\n\t\t\t}\n\t\t},\n\t\t[text, resumeId]\n\t);\n\n\tconst handleImprove = useCallback(async () => {\n\t\tif (!jobId) return;\n\n\t\tsetImprovementStatus('improving');\n\t\ttry {\n\t\t\tconst preview = await improveResume(resumeId, jobId);\n\t\t\tsetImprovedData(preview);\n\t\t\trouter.push('/dashboard');\n\t\t} catch (err) {\n\t\t\tconsole.error(err);\n\t\t\tsetImprovementStatus('error');\n\t\t\tsetFlash({ type: 'error', message: (err as Error).message });\n\t\t}\n\t}, [resumeId, jobId, setImprovedData, router]);\n\n\tconst isNextDisabled = text.trim() === '' || submissionStatus === 'submitting';\n\n\treturn (\n\t\t<form onSubmit={handleUpload} className=\"p-4 mx-auto w-full max-w-xl\">\n\t\t\t{flash && (\n\t\t\t\t<div\n\t\t\t\t\tclassName={`p-3 mb-4 text-sm rounded-md ${flash.type === 'error'\n\t\t\t\t\t\t? 'bg-red-50 border border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800/30 dark:text-red-300'\n\t\t\t\t\t\t: 'bg-green-50 border border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800/30 dark:text-green-300'\n\t\t\t\t\t\t}`}\n\t\t\t\t\trole=\"alert\"\n\t\t\t\t>\n\t\t\t\t\t<p>{flash.message}</p>\n\t\t\t\t</div>\n\t\t\t)}\n\n\t\t\t<div className=\"mb-6 relative\">\n\t\t\t\t<label\n\t\t\t\t\thtmlFor=\"jobDescription\"\n\t\t\t\t\tclassName=\"bg-zinc-950/80 text-white absolute start-1 top-0 z-10 block -translate-y-1/2 px-2 text-xs font-medium group-has-disabled:opacity-50\"\n\t\t\t\t>\n\t\t\t\t\tJob Description <span className=\"text-red-500\">*</span>\n\t\t\t\t</label>\n\t\t\t\t<Textarea\n\t\t\t\t\tid=\"jobDescription\"\n\t\t\t\t\trows={15}\n\t\t\t\t\tvalue={text}\n\t\t\t\t\tonChange={handleChange}\n\t\t\t\t\trequired\n\t\t\t\t\taria-required=\"true\"\n\t\t\t\t\tplaceholder=\"Paste the job description here...\"\n\t\t\t\t\tclassName=\"w-full bg-gray-800/30 focus:ring-1 border rounded-md dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500/50 border-gray-300 min-h-[300px]\"\n\t\t\t\t/>\n\t\t\t</div>\n\n\t\t\t<div className=\"flex justify-end pt-4\">\n\t\t\t\t<Button\n\t\t\t\t\ttype=\"submit\"\n\t\t\t\t\tdisabled={isNextDisabled}\n\t\t\t\t\taria-disabled={isNextDisabled}\n\t\t\t\t\tclassName={`font-semibold py-2 px-6 rounded flex items-center justify-center min-w-[90px] transition-all duration-200 ease-in-out ${isNextDisabled\n\t\t\t\t\t\t? 'bg-gray-400 dark:bg-gray-600 text-gray-600 dark:text-gray-400 cursor-not-allowed'\n\t\t\t\t\t\t: 'bg-blue-600 hover:bg-blue-700 text-white dark:bg-blue-500 dark:hover:bg-blue-600'\n\t\t\t\t\t\t}`}\n\t\t\t\t>\n\t\t\t\t\t{submissionStatus === 'submitting' ? (\n\t\t\t\t\t\t<>\n\t\t\t\t\t\t\t<svg\n\t\t\t\t\t\t\t\tclassName=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\"\n\t\t\t\t\t\t\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\t\t\t\t\t\t\tfill=\"none\"\n\t\t\t\t\t\t\t\tviewBox=\"0 0 24 24\"\n\t\t\t\t\t\t\t\taria-hidden=\"true\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" />\n\t\t\t\t\t\t\t\t<path\n\t\t\t\t\t\t\t\t\tclassName=\"opacity-75\"\n\t\t\t\t\t\t\t\t\tfill=\"currentColor\"\n\t\t\t\t\t\t\t\t\td=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t<span>Submitting...</span>\n\t\t\t\t\t\t</>\n\t\t\t\t\t) : submissionStatus === 'success' ? (\n\t\t\t\t\t\t<span>Submitted!</span>\n\t\t\t\t\t) : (\n\t\t\t\t\t\t<span>Next</span>\n\t\t\t\t\t)}\n\t\t\t\t</Button>\n\t\t\t</div>\n\n\t\t\t{submissionStatus === 'success' && jobId && (\n\t\t\t\t<div className=\"flex justify-end mt-2\">\n\t\t\t\t\t<Button\n\t\t\t\t\t\tonClick={handleImprove}\n\t\t\t\t\t\tdisabled={improvementStatus === 'improving'}\n\t\t\t\t\t\tclassName=\"font-semibold py-2 px-6 rounded min-w-[90px] bg-green-600 hover:bg-green-700 text-white\"\n\t\t\t\t\t>\n\t\t\t\t\t\t{improvementStatus === 'improving' ? 'Improving...' : 'Improve'}\n\t\t\t\t\t</Button>\n\t\t\t\t</div>\n\t\t\t)}\n\t\t</form>\n\t);\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAYe,SAAS;IACvB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyD;IAC1F,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAC3E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IAC9E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,mBAAgB,AAAD;IAC3C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,IAAI,GAAG,CAAC;IACvC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,CAAC;QACA,QAAQ,EAAE,MAAM,CAAC,KAAK;QACtB,SAAS;QACT,IAAI,qBAAqB,QAAQ,oBAAoB;IACtD,GACA;QAAC;KAAiB;IAGnB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,OAAO;QACN,EAAE,cAAc;QAChB,MAAM,UAAU,KAAK,IAAI;QACzB,IAAI,CAAC,SAAS;YACb,SAAS;gBAAE,MAAM;gBAAS,SAAS;YAAmC;YACtE;QACD;QACA,IAAI,CAAC,UAAU;YACd,SAAS;gBAAE,MAAM;gBAAS,SAAS;YAAqB;YACxD;QACD;QAEA,oBAAoB;QACpB,IAAI;YACH,MAAM,KAAK,MAAM,CAAA,GAAA,oHAAA,CAAA,wBAAqB,AAAD,EAAE;gBAAC;aAAQ,EAAE;YAClD,SAAS;YACT,oBAAoB;YACpB,SAAS;gBAAE,MAAM;gBAAW,SAAS;YAA0C;QAChF,EAAE,OAAO,KAAK;YACb,QAAQ,KAAK,CAAC;YACd,oBAAoB;YACpB,SAAS;gBAAE,MAAM;gBAAS,SAAS,AAAC,IAAc,OAAO;YAAC;QAC3D;IACD,GACA;QAAC;QAAM;KAAS;IAGjB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,IAAI,CAAC,OAAO;QAEZ,qBAAqB;QACrB,IAAI;YACH,MAAM,UAAU,MAAM,CAAA,GAAA,oHAAA,CAAA,gBAAa,AAAD,EAAE,UAAU;YAC9C,gBAAgB;YAChB,OAAO,IAAI,CAAC;QACb,EAAE,OAAO,KAAK;YACb,QAAQ,KAAK,CAAC;YACd,qBAAqB;YACrB,SAAS;gBAAE,MAAM;gBAAS,SAAS,AAAC,IAAc,OAAO;YAAC;QAC3D;IACD,GAAG;QAAC;QAAU;QAAO;QAAiB;KAAO;IAE7C,MAAM,iBAAiB,KAAK,IAAI,OAAO,MAAM,qBAAqB;IAElE,qBACC,8OAAC;QAAK,UAAU;QAAc,WAAU;;YACtC,uBACA,8OAAC;gBACA,WAAW,CAAC,4BAA4B,EAAE,MAAM,IAAI,KAAK,UACtD,6GACA,wHACA;gBACH,MAAK;0BAEL,cAAA,8OAAC;8BAAG,MAAM,OAAO;;;;;;;;;;;0BAInB,8OAAC;gBAAI,WAAU;;kCACd,8OAAC;wBACA,SAAQ;wBACR,WAAU;;4BACV;0CACgB,8OAAC;gCAAK,WAAU;0CAAe;;;;;;;;;;;;kCAEhD,8OAAC,6HAAA,CAAA,WAAQ;wBACR,IAAG;wBACH,MAAM;wBACN,OAAO;wBACP,UAAU;wBACV,QAAQ;wBACR,iBAAc;wBACd,aAAY;wBACZ,WAAU;;;;;;;;;;;;0BAIZ,8OAAC;gBAAI,WAAU;0BACd,cAAA,8OAAC,2HAAA,CAAA,SAAM;oBACN,MAAK;oBACL,UAAU;oBACV,iBAAe;oBACf,WAAW,CAAC,sHAAsH,EAAE,iBACjI,qFACA,oFACA;8BAEF,qBAAqB,6BACrB;;0CACC,8OAAC;gCACA,WAAU;gCACV,OAAM;gCACN,MAAK;gCACL,SAAQ;gCACR,eAAY;;kDAEZ,8OAAC;wCAAO,WAAU;wCAAa,IAAG;wCAAK,IAAG;wCAAK,GAAE;wCAAK,QAAO;wCAAe,aAAY;;;;;;kDACxF,8OAAC;wCACA,WAAU;wCACV,MAAK;wCACL,GAAE;;;;;;;;;;;;0CAGJ,8OAAC;0CAAK;;;;;;;uCAEJ,qBAAqB,0BACxB,8OAAC;kCAAK;;;;;6CAEN,8OAAC;kCAAK;;;;;;;;;;;;;;;;YAKR,qBAAqB,aAAa,uBAClC,8OAAC;gBAAI,WAAU;0BACd,cAAA,8OAAC,2HAAA,CAAA,SAAM;oBACN,SAAS;oBACT,UAAU,sBAAsB;oBAChC,WAAU;8BAET,sBAAsB,cAAc,iBAAiB;;;;;;;;;;;;;;;;;AAM5D", "debugId": null}}, {"offset": {"line": 450, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Resume-Matcher-main/apps/frontend/components/common/dot-pattern-glow.tsx"], "sourcesContent": ["'use client';\n\nimport { cn } from '@/lib/utils';\nimport { motion } from 'motion/react';\nimport React, { useEffect, useId, useRef, useState } from 'react';\n\n/**\n *  DotPattern Component Props\n *\n * @param {number} [width=16] - The horizontal spacing between dots\n * @param {number} [height=16] - The vertical spacing between dots\n * @param {number} [x=0] - The x-offset of the entire pattern\n * @param {number} [y=0] - The y-offset of the entire pattern\n * @param {number} [cx=1] - The x-offset of individual dots\n * @param {number} [cy=1] - The y-offset of individual dots\n * @param {number} [cr=1] - The radius of each dot\n * @param {string} [className] - Additional CSS classes to apply to the SVG container\n * @param {boolean} [glow=false] - Whether dots should have a glowing animation effect\n */\ninterface DotPatternProps extends React.SVGProps<SVGSVGElement> {\n\twidth?: number;\n\theight?: number;\n\tx?: number;\n\ty?: number;\n\tcx?: number;\n\tcy?: number;\n\tcr?: number;\n\tclassName?: string;\n\tglow?: boolean;\n\t[key: string]: unknown;\n}\n\n/**\n * DotPattern Component\n *\n * A React component that creates an animated or static dot pattern background using SVG.\n * The pattern automatically adjusts to fill its container and can optionally display glowing dots.\n *\n * @component\n *\n * @see DotPatternProps for the props interface.\n *\n * @example\n * // Basic usage\n * <DotPattern />\n *\n * // With glowing effect and custom spacing\n * <DotPattern\n *   width={20}\n *   height={20}\n *   glow={true}\n *   className=\"opacity-50\"\n * />\n *\n * @notes\n * - The component is client-side only (\"use client\")\n * - Automatically responds to container size changes\n * - When glow is enabled, dots will animate with random delays and durations\n * - Uses Motion for animations\n * - Dots color can be controlled via the text color utility classes\n */\n\nexport function DotPattern({\n\twidth = 16,\n\theight = 16,\n\tx = 0,\n\ty = 0,\n\tcx = 1,\n\tcy = 1,\n\tcr = 1,\n\tclassName,\n\tglow = false,\n\t...props\n}: DotPatternProps) {\n\tconst id = useId();\n\tconst containerRef = useRef<SVGSVGElement>(null);\n\tconst [dimensions, setDimensions] = useState({ width: 0, height: 0 });\n\n\tuseEffect(() => {\n\t\tconst updateDimensions = () => {\n\t\t\tif (containerRef.current) {\n\t\t\t\tconst { width, height } = containerRef.current.getBoundingClientRect();\n\t\t\t\tsetDimensions({ width, height });\n\t\t\t}\n\t\t};\n\n\t\tupdateDimensions();\n\t\twindow.addEventListener('resize', updateDimensions);\n\t\treturn () => window.removeEventListener('resize', updateDimensions);\n\t}, []);\n\n\tconst dots = Array.from(\n\t\t{\n\t\t\tlength: Math.ceil(dimensions.width / width) * Math.ceil(dimensions.height / height),\n\t\t},\n\t\t(_, i) => {\n\t\t\tconst col = i % Math.ceil(dimensions.width / width);\n\t\t\tconst row = Math.floor(i / Math.ceil(dimensions.width / width));\n\t\t\treturn {\n\t\t\t\tx: col * width + cx,\n\t\t\t\ty: row * height + cy,\n\t\t\t\tdelay: Math.random() * 5,\n\t\t\t\tduration: Math.random() * 3 + 2,\n\t\t\t};\n\t\t},\n\t);\n\n\treturn (\n\t\t<svg\n\t\t\tref={containerRef}\n\t\t\taria-hidden=\"true\"\n\t\t\tclassName={cn('pointer-events-none absolute inset-0 h-full w-full', className)}\n\t\t\tx={x}\n\t\t\ty={y}\n\t\t\t{...props}\n\t\t>\n\t\t\t<defs>\n\t\t\t\t<radialGradient id={`${id}-gradient`}>\n\t\t\t\t\t<stop offset=\"0%\" stopColor=\"currentColor\" stopOpacity=\"1\" />\n\t\t\t\t\t<stop offset=\"100%\" stopColor=\"currentColor\" stopOpacity=\"0\" />\n\t\t\t\t</radialGradient>\n\t\t\t</defs>\n\t\t\t{dots.map((dot) => (\n\t\t\t\t<motion.circle\n\t\t\t\t\tkey={`${dot.x}-${dot.y}`}\n\t\t\t\t\tcx={dot.x}\n\t\t\t\t\tcy={dot.y}\n\t\t\t\t\tr={cr}\n\t\t\t\t\tfill={glow ? `url(#${id}-gradient)` : 'currentColor'}\n\t\t\t\t\tclassName=\"text-lime-400\"\n\t\t\t\t\tinitial={glow ? { opacity: 0.4, scale: 1 } : {}}\n\t\t\t\t\tanimate={\n\t\t\t\t\t\tglow\n\t\t\t\t\t\t\t? {\n\t\t\t\t\t\t\t\t\topacity: [0.4, 1, 0.4],\n\t\t\t\t\t\t\t\t\tscale: [1, 1.5, 1],\n\t\t\t\t\t\t\t  }\n\t\t\t\t\t\t\t: {}\n\t\t\t\t\t}\n\t\t\t\t\ttransition={\n\t\t\t\t\t\tglow\n\t\t\t\t\t\t\t? {\n\t\t\t\t\t\t\t\t\tduration: dot.duration,\n\t\t\t\t\t\t\t\t\trepeat: Infinity,\n\t\t\t\t\t\t\t\t\trepeatType: 'reverse',\n\t\t\t\t\t\t\t\t\tdelay: dot.delay,\n\t\t\t\t\t\t\t\t\tease: 'easeInOut',\n\t\t\t\t\t\t\t  }\n\t\t\t\t\t\t\t: {}\n\t\t\t\t\t}\n\t\t\t\t/>\n\t\t\t))}\n\t\t</svg>\n\t);\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AA8DO,SAAS,WAAW,EAC1B,QAAQ,EAAE,EACV,SAAS,EAAE,EACX,IAAI,CAAC,EACL,IAAI,CAAC,EACL,KAAK,CAAC,EACN,KAAK,CAAC,EACN,KAAK,CAAC,EACN,SAAS,EACT,OAAO,KAAK,EACZ,GAAG,OACc;IACjB,MAAM,KAAK,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD;IACf,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAiB;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,OAAO;QAAG,QAAQ;IAAE;IAEnE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACT,MAAM,mBAAmB;YACxB,IAAI,aAAa,OAAO,EAAE;gBACzB,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,aAAa,OAAO,CAAC,qBAAqB;gBACpE,cAAc;oBAAE;oBAAO;gBAAO;YAC/B;QACD;QAEA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACnD,GAAG,EAAE;IAEL,MAAM,OAAO,MAAM,IAAI,CACtB;QACC,QAAQ,KAAK,IAAI,CAAC,WAAW,KAAK,GAAG,SAAS,KAAK,IAAI,CAAC,WAAW,MAAM,GAAG;IAC7E,GACA,CAAC,GAAG;QACH,MAAM,MAAM,IAAI,KAAK,IAAI,CAAC,WAAW,KAAK,GAAG;QAC7C,MAAM,MAAM,KAAK,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,WAAW,KAAK,GAAG;QACxD,OAAO;YACN,GAAG,MAAM,QAAQ;YACjB,GAAG,MAAM,SAAS;YAClB,OAAO,KAAK,MAAM,KAAK;YACvB,UAAU,KAAK,MAAM,KAAK,IAAI;QAC/B;IACD;IAGD,qBACC,8OAAC;QACA,KAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sDAAsD;QACpE,GAAG;QACH,GAAG;QACF,GAAG,KAAK;;0BAET,8OAAC;0BACA,cAAA,8OAAC;oBAAe,IAAI,GAAG,GAAG,SAAS,CAAC;;sCACnC,8OAAC;4BAAK,QAAO;4BAAK,WAAU;4BAAe,aAAY;;;;;;sCACvD,8OAAC;4BAAK,QAAO;4BAAO,WAAU;4BAAe,aAAY;;;;;;;;;;;;;;;;;YAG1D,KAAK,GAAG,CAAC,CAAC,oBACV,8OAAC,kNAAA,CAAA,SAAM,CAAC,MAAM;oBAEb,IAAI,IAAI,CAAC;oBACT,IAAI,IAAI,CAAC;oBACT,GAAG;oBACH,MAAM,OAAO,CAAC,KAAK,EAAE,GAAG,UAAU,CAAC,GAAG;oBACtC,WAAU;oBACV,SAAS,OAAO;wBAAE,SAAS;wBAAK,OAAO;oBAAE,IAAI,CAAC;oBAC9C,SACC,OACG;wBACA,SAAS;4BAAC;4BAAK;4BAAG;yBAAI;wBACtB,OAAO;4BAAC;4BAAG;4BAAK;yBAAE;oBAClB,IACA,CAAC;oBAEL,YACC,OACG;wBACA,UAAU,IAAI,QAAQ;wBACtB,QAAQ;wBACR,YAAY;wBACZ,OAAO,IAAI,KAAK;wBAChB,MAAM;oBACN,IACA,CAAC;mBAxBA,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE;;;;;;;;;;;AA8B7B", "debugId": null}}]}